import axios from "axios";
import * as cheerio from "cheerio";
import { In } from "typeorm";
import { StatusCodes } from "http-status-codes";
import {
  getProductRepo,
  getVendorRepo,
} from "../../../models/product/index.js";
import { CustomLogger } from "../../utils/logger/logger.js";
import { BaseAPIResponse } from "../../utils/responses/index.js";
import { CustomApiError } from "../../utils/errors/index.js";
import { analyzeAndRefineSearchTerm } from "../admin/searchtermanalysis.controller.js";
import { getHistoryRepo } from "../../../models/product/history.entity.js";
import { getUserRepo } from "../../../models/user/user.entity.js";
import { getConversationRepo, getMessageRepo } from "../../../models/chat/index.js";
import { generateContextualResponse } from "../../third-party/OpenAI/index.js";

export const scrapeAndSaveAlibabaProducts = async (req, res) => {
  const {
    query: rawQuery,
    gender = "both",
    category = "Not Found",
    userId: queryUserId,
    page = 1,
    limit = 8,
  } = req.query;

  // validate query
  if (!rawQuery) {
    return res
      .status(StatusCodes.BAD_REQUEST)
      .json(new CustomApiError(StatusCodes.BAD_REQUEST, "Query is required"));
  }

  const { refinedKeyword, category: normalizedCategory } =
    await analyzeAndRefineSearchTerm(rawQuery);

  // ✅ Use user ID from auth or query
  const userId = (req.user?.id || queryUserId)?.trim();
  try {
    if (userId) {
      const user = await getUserRepo().findOne({ where: { id: userId } });
      if (user) {
        const historyRepo = getHistoryRepo();
        const lastHistory = await historyRepo.findOne({
          where: { user: { id: userId } },
          order: { createdAt: "DESC" },
        });

        if (!lastHistory || lastHistory.term !== rawQuery) {
          const history = historyRepo.create({
            term: rawQuery,
            user,
          });
          await historyRepo.save(history);
        }
      } else {
        CustomLogger.warn("Invalid userId passed: not found in DB.");
      }
    }
  } catch (err) {
    CustomLogger.warn("Failed to save search history:", err.message || err);
  }

  const MAX_PER_REQUEST = 4;
  const numericPage = parseInt(page);
  const numericLimit = parseInt(limit);
  const requestCount = Math.ceil(numericLimit / MAX_PER_REQUEST);
  const startingPage = (numericPage - 1) * requestCount + 1;

  const urls = Array.from({ length: requestCount }, (_, i) => {
    return `https://www.alibaba.com/hzmagellanviptbsitenet/trade/search?SearchText=${encodeURIComponent(
      refinedKeyword
    )}&page=${startingPage + i}`;
  });

  const allProducts = [];

  try {
    for (const url of urls) {
      const { data: html } = await axios.get(url);
      const $ = cheerio.load(html);
      const cards = $(".fy23-search-card");

      if (cards.length === 0) continue;

      cards.each((_, el) => {
        try {
          const card = $(el);

          const title = card.find(".search-card-e-title span").text().trim();
          const price = card.find(".search-card-e-price-main").text().trim();
          const image = card.find(".search-card-e-slider__img").attr("src");

          let rawHref =
            card.find(".search-card-e-slider__link").attr("href")?.trim() || "";
          let productUrl = "not found";

          if (rawHref.startsWith("http")) {
            productUrl = rawHref.includes("alibaba.comhttps://www.alibaba.com")
              ? rawHref.replace(
                  "https://www.alibaba.comhttps://www.alibaba.com",
                  "https://www.alibaba.com"
                )
              : rawHref;
          } else if (rawHref.startsWith("//")) {
            productUrl = `https:${rawHref}`;
          } else if (rawHref) {
            productUrl = `https://www.alibaba.com${rawHref.startsWith("/") ? rawHref : "/" + rawHref}`;
          }

          const minOrder = card
            .find(".search-card-m-sale-features__item")
            .first()
            .text()
            .trim();
          const vendorName = card.find(".search-card-e-company").text().trim();
          const genderCategory = card
            .find(".search-card-e-gender")
            .text()
            .trim();

          if (!title || !price || !image || !productUrl || !vendorName) return;

          const isVerified =
            card.find('img[src*="H58367af07b91408ab045a753e6b0c41av"]').length >
            0;

          const rawVendorUrl =
            card.find("a.search-card-e-company").attr("href")?.trim() ||
            "not found";
          const vendorWebsiteUrl = rawVendorUrl.startsWith("http")
            ? rawVendorUrl
            : rawVendorUrl.startsWith("//")
              ? "https:" + rawVendorUrl
              : rawVendorUrl !== "not found"
                ? "https://" + rawVendorUrl
                : "not found";

          const rawYears = card
            .find(".search-card-e-supplier__year span")
            .first()
            .text()
            .trim();
          const yearsMatch = rawYears.match(/\d+/)?.[0];
          const years = yearsMatch
            ? `${yearsMatch} yr${yearsMatch === "1" ? "" : "s"}`
            : "Unknown";

          const rawCountry =
            card.find(".search-card-e-country-flag__wrapper img").attr("alt") ||
            "Unknown";
          const country =
            rawCountry.length === 2 ? rawCountry.toUpperCase() : rawCountry;

          const score =
            parseFloat(
              card.find(".search-card-e-review span").first().text().trim()
            ) || 0;
          const reviews =
            parseInt(
              card
                .find(".search-card-e-review span")
                .eq(1)
                .text()
                .replace(/[()]/g, "")
            ) || 0;

          allProducts.push({
            title,
            price,
            imageUrl: "https:" + image,
            productUrl,
            minimumOrderQuantity: minOrder,
            platform: "Alibaba",
            rating: score,
            customerInterestNumber: reviews,
            genderCategory: gender,
            productCategory: normalizedCategory,
            additionalDetails: {
              sourceUrl: productUrl,
              vendorName,
              yearsActive: years,
            },
            vendor: {
              name: vendorName,
              isVerified,
              websiteUrl: vendorWebsiteUrl,
              registerOn: new Date(),
              country,
              region: "Unknown",
              city: "Unknown",
              additional: {
                yearsActive: years,
              },
            },
          });
        } catch (e) {
          CustomLogger.warn(`Error parsing product card:\n${e.stack || e}`);
        }
      });
    }

    if (allProducts.length === 0) {
      return res.status(StatusCodes.OK).json(
        new BaseAPIResponse({
          statusCode: StatusCodes.OK,
          message: "No products extracted from Alibaba",
          data: { totalProducts: 0, page: numericPage, query: rawQuery },
        })
      );
    }

    const vendorRepo = getVendorRepo();
    const productRepo = getProductRepo();
    const savedVendorsMap = {};

    for (const p of allProducts) {
      let vendor = await vendorRepo.findOne({ where: { name: p.vendor.name } });
      if (!vendor) {
        vendor = vendorRepo.create(p.vendor);
      } else {
        vendor.numOfProducts += 1;
      }
      vendor = await vendorRepo.save(vendor);
      savedVendorsMap[p.vendor.name] = vendor;
    }

    const existingProducts = await productRepo.find({
      where: {
        title: In(allProducts.map((p) => p.title)),
      },
      select: ["title", "price", "platform"],
    });

    const toSave = allProducts
      .filter((p) => {
        return !existingProducts.some(
          (existing) =>
            existing.title === p.title &&
            existing.price === p.price &&
            existing.platform === p.platform
        );
      })
      .map((p) => ({
        imageUrl: p.imageUrl,
        productUrl: p.productUrl,
        title: p.title,
        price: p.price,
        minimumOrderQuantity: p.minimumOrderQuantity,
        platform: p.platform,
        rating: p.rating,
        customerInterestNumber: p.customerInterestNumber,
        genderCategory: p.genderCategory,
        productCategory: p.productCategory,
        additionalDetails: p.additionalDetails,
        vendor: savedVendorsMap[p.vendor.name],
      }));

    const saved = await productRepo.save(toSave);

    return res.status(StatusCodes.OK).json(
      new BaseAPIResponse({
        statusCode: StatusCodes.OK,
        message: "Products scraped and saved successfully",
        data: {
          totalProducts: saved.length,
          products: saved,
          page: numericPage,
          query: rawQuery,
          hasMore: saved.length >= numericLimit,
        },
      })
    );
  } catch (err) {
    CustomLogger.error("Error in scraping and saving:", err);
    return res
      .status(StatusCodes.INTERNAL_SERVER_ERROR)
      .json(
        new CustomApiError(
          StatusCodes.INTERNAL_SERVER_ERROR,
          "Error scraping and saving products"
        )
      );
  }
};

// Chat-integrated search endpoint
export const chatIntegratedSearch = async (req, res) => {
  try {
    const { conversationId, searchTerm } = req.body;
    const userId = req.user.id;

    if (!conversationId || !searchTerm) {
      return res.status(StatusCodes.BAD_REQUEST).json(
        new CustomApiError(StatusCodes.BAD_REQUEST, "Conversation ID and search term are required")
      );
    }

    // Verify conversation belongs to user
    const conversationRepo = getConversationRepo();
    const conversation = await conversationRepo.findOne({
      where: { id: conversationId, user: { id: userId } },
      relations: ["messages"]
    });

    if (!conversation) {
      return res.status(StatusCodes.NOT_FOUND).json(
        new CustomApiError(StatusCodes.NOT_FOUND, "Conversation not found")
      );
    }

    // Perform the product search using existing logic
    const { refinedKeyword, category: normalizedCategory } = await analyzeAndRefineSearchTerm(searchTerm);

    // Use the existing scraping logic but with refined parameters
    const searchResults = await performProductSearch(refinedKeyword, normalizedCategory, userId);

    // Generate contextual AI response based on search results
    const recentMessages = conversation.messages
      .sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt))
      .slice(-5);

    const contextualResponse = await generateContextualResponse(recentMessages, searchResults);

    // Save AI response as a message
    const messageRepo = getMessageRepo();
    const aiMessage = messageRepo.create({
      conversation: { id: conversationId },
      content: contextualResponse,
      role: "assistant",
      metadata: {
        searchTerm: refinedKeyword,
        resultsCount: searchResults.length,
        timestamp: new Date().toISOString()
      }
    });

    await messageRepo.save(aiMessage);

    // Update conversation context
    await conversationRepo.update(conversationId, {
      lastSearchTerm: refinedKeyword,
      updatedAt: new Date()
    });

    return res.status(StatusCodes.OK).json(
      new BaseAPIResponse({
        statusCode: StatusCodes.OK,
        message: "Chat-integrated search completed successfully",
        data: {
          searchTerm: refinedKeyword,
          products: searchResults,
          aiResponse: contextualResponse,
          conversationId
        }
      })
    );

  } catch (error) {
    CustomLogger.error("Error in chat-integrated search:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json(
      new CustomApiError(StatusCodes.INTERNAL_SERVER_ERROR, "Failed to perform chat-integrated search")
    );
  }
};

// Helper function to perform product search (extracted from existing logic)
const performProductSearch = async (searchTerm, category, userId, page = 1, limit = 8) => {
  const productRepo = getProductRepo();
  const vendorRepo = getVendorRepo();

  try {
    // This is a simplified version - you can integrate the full Alibaba scraping logic here
    const products = await productRepo.find({
      where: {
        title: searchTerm, // You can make this more sophisticated with LIKE queries
        productCategory: category
      },
      relations: ["vendor"],
      take: limit,
      skip: (page - 1) * limit
    });

    return products;
  } catch (error) {
    CustomLogger.error("Error in product search:", error);
    return [];
  }
};
