import { configureStore } from "@reduxjs/toolkit";
import authReducer from "./services/Auth/AuthSlice";
import { authApi } from "./services/Auth/AuthService";
import { userApi } from "./services/User/UserService";
import { scrapingApi } from "./services/Products/ScrapingApi";
import productReducer from "./services/Products/productSlice";
import { favouritesApi } from "./services/Favourites/FavouritesService";
import { historyApi } from "./services/History/HistoryService";

export const store = configureStore({
  reducer: {
    auth: authReducer,
    product: productReducer,
    [authApi.reducerPath]: authApi.reducer,
    [userApi.reducerPath]: userApi.reducer,
    [scrapingApi.reducerPath]: scrapingApi.reducer,
    [favouritesApi.reducerPath]: favouritesApi.reducer,
    [historyApi.reducerPath]: historyApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(
      authApi.middleware,
      userApi.middleware,
      scrapingApi.middleware,
      favouritesApi.middleware,
      historyApi.middleware
    ),
});

export type RootState = ReturnType<typeof store.getState>;
