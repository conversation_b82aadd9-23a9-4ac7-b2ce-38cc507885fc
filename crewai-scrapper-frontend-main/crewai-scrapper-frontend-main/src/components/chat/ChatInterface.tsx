import React, { useState, useRef, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../api/store";
import {
  closeChat,
  setTyping,
  addMessage,
  setCurrentConversation,
  setCurrentMessages,
  setLastSearchTerm,
  setSearching,
} from "../../api/services/Chat/ChatSlice";
import {
  useCreateConversationMutation,
  useAddMessageMutation,
  useChatIntegratedSearchMutation,
  useGetConversationQuery,
} from "../../api/services/Chat/ChatService";
import { X, Send, MessageCircle, Loader2, Lightbulb } from "lucide-react";
import MessageList from "./MessageList";
import ConversationList from "./ConversationList";
import { useChatSearch } from "../../hooks/useChatSearch";
import {
  extractSearchIntent,
  enhanceSearchQuery,
  generateSearchSuggestions,
  SearchContext
} from "../../utils/searchEnhancement";

interface ChatInterfaceProps {
  onProductsUpdate?: (products: any[], searchTerm: string) => void;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({ onProductsUpdate }) => {
  const dispatch = useDispatch();
  const {
    isChatOpen,
    activeConversationId,
    currentConversation,
    currentMessages,
    isTyping,
    isSearching,
    chatWidth,
  } = useSelector((state: RootState) => state.chat);

  const [inputMessage, setInputMessage] = useState("");
  const [showConversations, setShowConversations] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [searchSuggestions, setSearchSuggestions] = useState<string[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);

  const { performChatSearch } = useChatSearch();

  const [createConversation, { isLoading: isCreating }] = useCreateConversationMutation();
  const [addMessage, { isLoading: isAddingMessage }] = useAddMessageMutation();
  const [chatSearch, { isLoading: isChatSearching }] = useChatIntegratedSearchMutation();

  // Get conversation data if we have an active conversation
  const { data: conversationData } = useGetConversationQuery(
    activeConversationId || "",
    { skip: !activeConversationId }
  );

  // Update local state when conversation data changes
  useEffect(() => {
    if (conversationData) {
      dispatch(setCurrentConversation(conversationData));
      dispatch(setCurrentMessages(conversationData.messages || []));
    }
  }, [conversationData, dispatch]);

  // Show loading state when fetching conversation
  const isLoadingConversation = activeConversationId && !conversationData && !currentConversation;

  // Focus input when chat opens
  useEffect(() => {
    if (isChatOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isChatOpen]);

  // Generate search suggestions when input changes
  useEffect(() => {
    if (inputMessage.trim().length > 2) {
      const context: SearchContext = {
        previousSearches: currentMessages
          .filter(msg => msg.searchTermGenerated)
          .map(msg => msg.searchTermGenerated!)
          .slice(-3),
        userPreferences: {},
        conversationHistory: currentMessages.map(msg => ({
          role: msg.role,
          content: msg.content,
          searchTerm: msg.searchTermGenerated
        }))
      };

      const suggestions = generateSearchSuggestions(inputMessage, context);
      setSearchSuggestions(suggestions);
      setShowSuggestions(suggestions.length > 0);
    } else {
      setShowSuggestions(false);
      setSearchSuggestions([]);
    }
  }, [inputMessage, currentMessages]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isCreating || isAddingMessage) return;

    const messageText = inputMessage.trim();
    setInputMessage("");
    setShowSuggestions(false);

    try {
      if (!activeConversationId) {
        // Create new conversation if none exists
        const result = await createConversation({ initialMessage: messageText }).unwrap();

        dispatch(setCurrentConversation(result.conversation));
        dispatch(setCurrentMessages(result.messages));
        dispatch(setLastSearchTerm(result.searchTerm));

        // Trigger product search with the AI-generated search term
        if (result.searchTerm && onProductsUpdate) {
          // Show searching state in chat
          dispatch(setSearching(true));
          onProductsUpdate([], result.searchTerm); // Pass empty array to trigger new search
          // setSearching(false) will be called by the parent component after search completes
        }
      } else {
        // Add message to existing conversation
        const result = await addMessage({
          conversationId: activeConversationId,
          message: messageText,
        }).unwrap();

        dispatch(addMessage(result.messages[0])); // User message
        dispatch(addMessage(result.messages[1])); // AI response
        dispatch(setLastSearchTerm(result.searchTerm));

        // Trigger product search with the new AI-generated search term
        if (result.searchTerm && onProductsUpdate) {
          // Show searching state in chat
          dispatch(setSearching(true));
          onProductsUpdate([], result.searchTerm); // Pass empty array to trigger new search
          // setSearching(false) will be called by the parent component after search completes
        }
      }
    } catch (error) {
      console.error("Failed to send message:", error);
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInputMessage(suggestion);
    setShowSuggestions(false);
    inputRef.current?.focus();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (!isChatOpen) return null;

  return (
    <div
      className="fixed top-0 right-0 h-full bg-white shadow-2xl border-l border-gray-200 z-50 flex flex-col"
      style={{ width: chatWidth }}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-purple-600 to-pink-600 text-white">
        <div className="flex items-center space-x-2">
          <MessageCircle className="h-5 w-5" />
          <h3 className="font-semibold">
            {currentConversation?.title || "AI Shopping Assistant"}
          </h3>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowConversations(!showConversations)}
            className="p-1 hover:bg-white/20 rounded"
            title="Conversations"
          >
            <MessageCircle className="h-4 w-4" />
          </button>
          <button
            onClick={() => dispatch(closeChat())}
            className="p-1 hover:bg-white/20 rounded"
            title="Close chat"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 flex">
        {/* Conversations sidebar */}
        {showConversations && (
          <div className="w-64 border-r border-gray-200">
            <ConversationList onSelectConversation={() => setShowConversations(false)} />
          </div>
        )}

        {/* Main chat area */}
        <div className="flex-1 flex flex-col">
          {/* Messages */}
          <div className="flex-1 overflow-hidden">
            {isLoadingConversation ? (
              <div className="flex-1 flex items-center justify-center p-8">
                <div className="text-center">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-purple-600" />
                  <p className="text-gray-600">Loading conversation...</p>
                </div>
              </div>
            ) : (
              <MessageList
                messages={currentMessages}
                isTyping={isTyping || isAddingMessage}
                isSearching={isSearching || isChatSearching}
              />
            )}
          </div>

          {/* Search suggestions */}
          {showSuggestions && searchSuggestions.length > 0 && (
            <div className="px-4 py-2 border-t border-gray-100 bg-gray-50">
              <div className="flex items-center space-x-2 mb-2">
                <Lightbulb className="h-4 w-4 text-yellow-500" />
                <span className="text-xs font-medium text-gray-600">Suggestions:</span>
              </div>
              <div className="space-y-1">
                {searchSuggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="block w-full text-left px-2 py-1 text-sm text-gray-700 hover:bg-purple-100 hover:text-purple-700 rounded transition-colors"
                  >
                    {suggestion}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Input area */}
          <div className="p-4 border-t border-gray-200">
            <div className="flex space-x-2">
              <input
                ref={inputRef}
                type="text"
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                onFocus={() => setShowSuggestions(searchSuggestions.length > 0)}
                onBlur={() => setTimeout(() => setShowSuggestions(false), 150)}
                placeholder={
                  activeConversationId
                    ? "Continue the conversation..."
                    : "Start a new search conversation..."
                }
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                disabled={isCreating || isAddingMessage}
              />
              <button
                onClick={handleSendMessage}
                disabled={!inputMessage.trim() || isCreating || isAddingMessage}
                className="px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
              >
                {isCreating || isAddingMessage ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Send className="h-4 w-4" />
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;
