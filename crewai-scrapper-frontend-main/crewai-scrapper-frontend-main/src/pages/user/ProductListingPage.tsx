import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useSearchParams, useNavigate } from "react-router-dom";
import { ArrowLeft, Brain, MessageCircle, Sparkles } from "lucide-react";
import { RootState } from "../../api/store";
import { useLazyScrapeProductQuery } from "../../api/services/Products/ScrapingApi";
import {
  setProducts,
  setLoading,
  setError,
  addProducts,
  clearProducts,
  setDynamicMaxPrice,
} from "../../api/services/Products/productSlice";
import ProductGrid from "../../components/product/ProductGrid";
import { Product } from "../../types/Product";
import ModernFilterSection from "../../components/filters/ModernFilterSection";
import { useGetSearchHistoryQuery } from "../../api/services/History/HistoryService";
import { skipToken } from "@reduxjs/toolkit/query";
import { Cha<PERSON><PERSON>nterface, ChatToggle } from "../../components/chat";
import { setLastSearchTerm, setActiveConversation, setCurrentConversation, openChat, setSearching } from "../../api/services/Chat/ChatSlice";
import { useGetConversationQuery } from "../../api/services/Chat/ChatService";

const ProductListingPage: React.FC = () => {
  const { products } = useSelector((state: RootState) => state.product);
  const dispatch = useDispatch();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoadMoreLoading, setIsLoadMoreLoading] = useState(false);
  const [dynamicMaxPrice, setDynamicMaxPriceState] = useState<number>(10000);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [filterResetSignal, setFilterResetSignal] = useState(0);
  const [searching, setSearching] = useState(false);
  const [hasSearchedOnce, setHasSearchedOnce] = useState(false);
  const { isAuthenticated, user } = useSelector(
    (state: RootState) => state.auth
  );
  const { isChatOpen, chatWidth } = useSelector((state: RootState) => state.chat);

  const searchTerm = searchParams.get("search") || "";
  const originalSearchTerm = searchParams.get("original") || "";
  const conversationId = searchParams.get("conversation") || "";

  const [triggerScrapeProduct, { data, isLoading, error }] =
    useLazyScrapeProductQuery();
  const {
    refetch, // get this from useGetSearchHistoryQuery
  } = useGetSearchHistoryQuery(isAuthenticated ? undefined : skipToken);

  // Load conversation in background if conversation ID exists
  const { data: conversationData } = useGetConversationQuery(
    conversationId || "",
    { skip: !conversationId }
  );

  const productsRef = useRef<Product[]>([]);
  productsRef.current = products;

  //   useEffect(() => {
  //     const fetchProducts = async () => {
  //       if (searchTerm) {
  //         dispatch(clearProducts());
  //         setCurrentPage(1);
  //         setHasMore(true);
  //         dispatch(setLoading(true));
  //         dispatch(setError(null));
  //         setSearching(true);

  //         try {
  //           await triggerScrapeProduct({
  //             query: searchTerm,
  //             page: 1,
  //             userId: isAuthenticated ? user?.id : null,
  //           });
  //         } catch (error) {
  //           console.error("Failed to fetch products:", error);
  //         } finally {
  //           setSearching(false);
  //           setHasSearchedOnce(true);
  //         }
  //       }
  //     };

  //     fetchProducts();

  //   }, [searchTerm, dispatch, triggerScrapeProduct, isAuthenticated, user?.id]);

  useEffect(() => {
    const fetchProducts = async () => {
      if (searchTerm) {
        dispatch(clearProducts());
        setCurrentPage(1);
        setHasMore(true);
        dispatch(setLoading(true));
        dispatch(setError(null));
        setSearching(true);

        try {
          await triggerScrapeProduct({
            query: searchTerm,
            page: 1,
            userId: isAuthenticated ? user?.id : null,
          });

          if (isAuthenticated) {
            await refetch(); // ⬅️ Fetch latest search history from server
          }
        } catch (error) {
          console.error("Failed to fetch products:", error);
        } finally {
          setSearching(false);
          setHasSearchedOnce(true);
        }
      }
    };

    fetchProducts();
  }, [searchTerm, dispatch, triggerScrapeProduct, isAuthenticated, user?.id]);
  useEffect(() => {
    if (data) {
      const newProducts: Product[] =
        data.data?.products?.map((apiProduct: Product) => ({
          ...apiProduct,
        })) || [];

      const existingIds = new Set(productsRef.current.map((p) => p.id));
      const uniqueNewProducts = newProducts.filter(
        (p) => !existingIds.has(p.id)
      );

      if (currentPage === 1) {
        dispatch(setProducts(uniqueNewProducts));
      } else {
        dispatch(addProducts(uniqueNewProducts));
      }

      const prices = uniqueNewProducts
        .map((p) => {
          const priceStr = p.price?.split("-")[0]?.replace(/[^0-9.]/g, "");
          return priceStr ? Number(priceStr) : undefined;
        })
        .filter((price): price is number => typeof price === "number");

      if (prices.length > 0) {
        const maxFoundPrice = Math.ceil(Math.max(...prices));
        setDynamicMaxPriceState(maxFoundPrice);
        dispatch(setDynamicMaxPrice(maxFoundPrice));
      }

      setHasMore(data.data?.hasMore);
      dispatch(setLoading(false));
    }
  }, [data, currentPage, dispatch]);

  useEffect(() => {
    if (error) {
      dispatch(setError("Failed to load products. Please try again."));
      dispatch(setLoading(false));
    }
  }, [error, dispatch]);

  useEffect(() => {
    dispatch(setLoading(isLoading));
  }, [isLoading, dispatch]);

  const loadMore = async () => {
    if (isLoading || isLoadMoreLoading || !hasMore) return;
    setIsLoadMoreLoading(true);
    const nextPage = currentPage + 1;
    setCurrentPage(nextPage);

    await triggerScrapeProduct({
      query: searchTerm,
      page: nextPage,
      userId: isAuthenticated ? user?.id : null,
    });

    setIsLoadMoreLoading(false);
    setFilterResetSignal((prev) => prev + 1);
  };

  useEffect(() => {
    setFilteredProducts(products);
  }, [products]);

  const handleBackToHome = () => {
    navigate("/chat");
  };

  // Handle products update from chat
  const handleChatProductsUpdate = async (newProducts: Product[], searchTerm: string) => {
    dispatch(setLastSearchTerm(searchTerm));

    if (newProducts.length === 0) {
      // Empty array means we should trigger a new search with the AI-generated term
      dispatch(clearProducts());
      setCurrentPage(1);
      setHasMore(true);
      dispatch(setLoading(true));
      dispatch(setError(null));
      setSearching(true);

      try {
        await triggerScrapeProduct({
          query: searchTerm,
          page: 1,
          userId: isAuthenticated ? user?.id : null,
        });

        if (isAuthenticated) {
          await refetch();
        }
      } catch (error) {
        console.error("Failed to fetch products:", error);
      } finally {
        setSearching(false);
        setHasSearchedOnce(true);
        // Turn off chat searching indicator
        dispatch(setSearching(false));
      }

      // Update URL to reflect the new search
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set("search", searchTerm);
      navigate(`/products?${newSearchParams.toString()}`, { replace: true });
    } else {
      // Direct product update (for future use)
      dispatch(clearProducts());
      dispatch(setProducts(newProducts));
      setCurrentPage(1);
      setHasMore(newProducts.length >= 8);
      setFilterResetSignal(prev => prev + 1);
    }
  };

  // Load conversation data in background when available
  useEffect(() => {
    if (conversationData) {
      dispatch(setActiveConversation(conversationData.id));
      dispatch(setCurrentConversation(conversationData));
      dispatch(setLastSearchTerm(conversationData.lastSearchTerm || searchTerm));

      // If we have conversation data, it means we came from AI search
      // Keep the chat open to show the conversation
      if (conversationData.messages && conversationData.messages.length > 0) {
        // Chat should already be open from landing page, but ensure it's visible
        // Don't auto-open here to avoid forcing it, let user control it
      }
    }
  }, [conversationData, dispatch, searchTerm]);

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Main content area - products */}
      <div
        className="flex-1 flex flex-col transition-all duration-300"
        style={{
          marginRight: isChatOpen ? `${chatWidth}px` : '0px'
        }}
      >
        <div className="p-6 overflow-y-auto">
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={handleBackToHome}
              className="flex items-center px-3 py-2 text-gray-600 hover:text-pink-600 hover:bg-pink-50 rounded-lg transition-colors duration-200"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </button>
          </div>

          {/* AI Processing Indicator */}
          {(originalSearchTerm || conversationId) && (
            <div className="mb-6 bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-xl p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2">
                    <Brain className="h-5 w-5 text-purple-600" />
                    <Sparkles className="h-4 w-4 text-pink-500" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-800">
                      AI Enhanced Search
                    </p>
                    <p className="text-xs text-gray-600">
                      {originalSearchTerm ? (
                        <>
                          Transformed "{originalSearchTerm}" → "{searchTerm}"
                        </>
                      ) : (
                        <>Showing AI-optimized results for "{searchTerm}"</>
                      )}
                    </p>
                  </div>
                </div>
                {conversationId && (
                  <button
                    onClick={() => dispatch(openChat())}
                    className="flex items-center space-x-2 px-3 py-2 bg-white border border-purple-200 rounded-lg hover:bg-purple-50 transition-colors text-sm"
                  >
                    <MessageCircle className="h-4 w-4 text-purple-600" />
                    <span className="text-purple-700 font-medium">View Chat</span>
                  </button>
                )}
              </div>
            </div>
          )}

          <ModernFilterSection
            allProducts={products}
            onFiltered={setFilteredProducts}
            maxPriceLimit={dynamicMaxPrice}
            resetSignal={filterResetSignal}
          />

          <ProductGrid products={filteredProducts} searchTerm={searchTerm} />

          {products.length > 0 && (
            <div className="flex flex-col items-center mt-6">
              {hasMore ? (
                <button
                  onClick={loadMore}
                  className={`px-6 py-2 bg-pink-400 text-white rounded-lg shadow hover:bg-pink-500 transition flex items-center justify-center gap-2 ${
                    isLoadMoreLoading ? "opacity-80 cursor-not-allowed" : ""
                  }`}
                  disabled={isLoadMoreLoading}
                >
                  {isLoadMoreLoading ? (
                    <>
                      <span className="animate-spin rounded-full h-4 w-4 border-2 border-t-transparent border-white"></span>
                      Please wait, loading...
                    </>
                  ) : (
                    "Load More"
                  )}
                </button>
              ) : (
                <p className="text-gray-500">No more data</p>
              )}
            </div>
          )}

          {products.length > 0 &&
            filteredProducts.length === 0 &&
            !isLoading &&
            !error && (
              <p className="text-center text-gray-500 mt-6">
                No products match the selected filters.
              </p>
            )}

          {products.length === 0 &&
            !isLoading &&
            !searching &&
            !error &&
            hasSearchedOnce &&
            searchTerm && (
              <p className="text-center text-gray-500 mt-6">
                No products found for "{searchTerm}".
              </p>
            )}
        </div>
      </div>

      {/* Chat Interface */}
      <ChatInterface onProductsUpdate={handleChatProductsUpdate} />

      {/* Chat Toggle Button */}
      <ChatToggle />
    </div>
  );
};

export default ProductListingPage;
