import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithReauth } from "../../interceptor-util";

// ----------------------------- Scraping API -----------------------------
export const scrapingApi = createApi({
  reducerPath: "scrapingApi",
  keepUnusedDataFor: 0,
  baseQuery: baseQueryWithReauth,

  endpoints: (builder) => ({
    scrapeProduct: builder.query({
      query: ({ query, page = 1, userId }) => {
        let url = `/scraping?query=${encodeURIComponent(query)}&page=${page}`;
        if (userId) {
          url += `&userId=${encodeURIComponent(userId)}`;
        }
        return {
          url,
          method: "GET",
        };
      },
    }),
    analyzeSearchTerm: builder.mutation({
      query: ({ searchTerm }) => ({
        url: `/admin/analyze-search-term`,
        method: "POST",
        body: { searchTerm },
      }),
    }),
  }),
});

// Hooks
export const { useLazyScrapeProductQuery, useAnalyzeSearchTermMutation } =
  scrapingApi;
