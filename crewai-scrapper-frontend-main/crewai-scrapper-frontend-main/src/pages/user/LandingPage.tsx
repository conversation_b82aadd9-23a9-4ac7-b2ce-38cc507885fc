import React, { useState } from "react";
import {
  ArrowRight,
  Search,
  ShoppingBag,
  Shirt,
  Footprints,
  Gem,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import Header from "../../components/layout/Header";
import { saveSearchToHistory } from "../../utils/searchHistory";
import SubscriptionContainer from "../../components/subscription/SubscriptionContainer";
import Features from "../../components/LandingPageContainer/Features";
import HowItWorks from "../../components/LandingPageContainer/HowItWorks";
import FAQ from "../../components/LandingPageContainer/FAQ";
import Footer from "../../components/layout/Footer";
import { startNewConversation } from "../../api/services/Chat/ChatSlice";
import { useCreateConversationMutation } from "../../api/services/Chat/ChatService";
import { toast } from "react-toastify";

const quickSearches = [
  {
    label: "Handbags",
    color: "from-pink-500 to-yellow-300",
    icon: ShoppingBag,
  },
  { label: "T-Shirts", color: "from-blue-500 to-purple-400", icon: Shirt },
  { label: "Shoes", color: "from-purple-500 to-pink-500", icon: Footprints },
  { label: "Jewelry", color: "from-yellow-400 to-orange-400", icon: Gem },
];

function LandingPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [location, setLocation] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [createConversation] = useCreateConversationMutation();

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim() && !isProcessing) {
      await processSearchWithAI(searchQuery);
      setSearchQuery("");
      setLocation("");
    }
  };

  const handleQuickSearch = async (searchTerm: string) => {
    if (!isProcessing) {
      await processSearchWithAI(searchTerm);
    }
  };

  const processSearchWithAI = async (searchTerm: string) => {
    setIsProcessing(true);

    try {
      saveSearchToHistory(searchTerm);

      // Show processing toast
      toast.info("🤖 AI is processing your search...", {
        position: "top-center",
        autoClose: 2000,
      });

      // Step 1: Create conversation with AI (this processes the search term)
      const result = await createConversation({
        initialMessage: searchTerm
      }).unwrap();

      // Show success toast
      toast.success(`✨ AI enhanced your search: "${result.searchTerm}"`, {
        position: "top-center",
        autoClose: 3000,
      });

      // Step 2: Navigate to products page with AI-enhanced search term
      const enhancedSearchTerm = result.searchTerm;
      navigate(
        `/products?search=${encodeURIComponent(enhancedSearchTerm)}&original=${encodeURIComponent(searchTerm)}&conversation=${result.conversation.id}`
      );

    } catch (error) {
      console.error("AI processing failed:", error);
      toast.error("AI processing failed, using original search", {
        position: "top-center",
        autoClose: 3000,
      });
      // Fallback to original search if AI fails
      navigate(`/products?search=${encodeURIComponent(searchTerm)}`);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <main className="w-full min-h-screen bg-gradient-to-b from-white via-pink-100 to-pink-200">
      <div className="relative min-h-screen w-full">
        <Header />
        <div className="mx-auto px-3 sm:px-4 lg:px-6 py-16 sm:py-20 lg:py-24 relative z-10 w-full">
          <div className="text-center pt-16 sm:pt-20 lg:pt-32 max-w-6xl mx-auto">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-extrabold mb-4 leading-tight px-2">
              <span className="text-gray-800">Find Products and </span>
              <span className="bg-gradient-to-r from-[#F545D4] via-[#BA68C8] to-[#F0BD57] bg-clip-text text-transparent">
                B2B Insights with AI
              </span>
            </h1>
            <p className="text-base sm:text-lg lg:text-xl text-gray-700 mb-10 max-w-2xl mx-auto leading-relaxed font-medium">
              Discover global suppliers, compare prices, and access product data
              — all in one place.
            </p>

            {/* Search Form */}
            <form onSubmit={handleSearch}>
              <div className="max-w-4xl mx-auto bg-white/90 backdrop-blur-sm rounded-3xl shadow-2xl p-8 mb-12 space-y-6 border border-white/20">
                {/* Input Fields */}
                <div className="flex flex-col lg:flex-row gap-4">
                  <div className="flex-1 relative">
                    <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                    <input
                      type="text"
                      placeholder="Find me a wholesale clothing supplier"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-12 pr-4 py-4 border border-gray-200 outline-none rounded-xl text-lg bg-white/80 backdrop-blur-sm"
                    />
                  </div>
                  <button
                    type="submit"
                    disabled={isProcessing}
                    className="bg-gradient-to-r from-[#F545D4] via-[#BA68C8] to-[#F0BD57] text-white px-8 py-4 rounded-xl transition-all duration-300 font-semibold text-lg flex items-center justify-center group shadow-lg hover:scale-105 disabled:opacity-70 disabled:cursor-not-allowed"
                  >
                    {isProcessing ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mr-2"></div>
                        Processing with AI...
                      </>
                    ) : (
                      <>
                        Search
                        <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                      </>
                    )}
                  </button>
                </div>

                {/* Quick Search */}
                <div className="space-y-6">
                  <h3 className="text-sm text-gray-600 font-semibold uppercase tracking-wide">
                    Quick Search
                  </h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    {quickSearches.map((item, index) => (
                      <button
                        key={index}
                        onClick={() => handleQuickSearch(item.label)}
                        className="group relative overflow-hidden bg-white rounded-xl p-4 shadow-md hover:shadow-lg transition-all duration-300 border border-gray-100 hover:scale-[1.03]"
                      >
                        <div
                          className={`absolute inset-0 bg-gradient-to-r ${item.color} opacity-0 group-hover:opacity-10 transition-opacity duration-300`}
                        ></div>
                        <div className="relative flex flex-col items-center space-y-2">
                          <div
                            className={`p-2 rounded-lg bg-gradient-to-r ${item.color}`}
                          >
                            <item.icon className="h-5 w-5 text-white" />
                          </div>
                          <span className="text-sm font-medium text-gray-700 group-hover:text-gray-900 transition-colors">
                            {item.label}
                          </span>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Popular Searches */}
                <div className="space-y-6">
                  <h3 className="text-sm text-gray-600 font-semibold uppercase tracking-wide mb-3">
                    Popular Searches
                  </h3>
                  <div className="flex flex-wrap gap-3 justify-center">
                    {[
                      "Lululemon suppliers",
                      "Nike",
                      "Adidas",
                      "Victoria Secret",
                      "Designer Bags",
                    ].map((term, idx) => (
                      <button
                        key={idx}
                        onClick={() => handleQuickSearch(term)}
                        className="px-4 py-2 bg-pink-100 text-pink-700 hover:bg-pink-200 rounded-full text-sm font-medium shadow-sm hover:shadow transition-all hover:scale-105"
                      >
                        {term}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>

        {/* Why choose Invendora */}

        <div>
          <Features />
        </div>

        <div>
          <HowItWorks />
        </div>

        {/* Subscription */}
        <div className="w-full pt-4 sm:px-4 md:px-6 lg:px-8">
          <SubscriptionContainer />
        </div>

        <div>
          <FAQ />
        </div>

        <div>
          <Footer />
        </div>
      </div>
    </main>
  );
}

export default LandingPage;
