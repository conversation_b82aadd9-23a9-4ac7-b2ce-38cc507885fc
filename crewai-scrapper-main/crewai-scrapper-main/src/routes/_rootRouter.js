import { Router } from "express";

import { authRouter } from "./auth.router.js";
import { userRouter } from "./user.router.js";
import { adminRouter } from "./admin.router.js";
import { scrapingRouter } from "./scraping.router.js";
import { favouriteRouter } from "./favourites.router.js";
import { historyRouter } from "./history.router.js";

export const rootRouter = Router();

rootRouter.use(authRouter);
rootRouter.use(userRouter);
rootRouter.use(adminRouter);
rootRouter.use(scrapingRouter);
rootRouter.use(favouriteRouter);
rootRouter.use(historyRouter);
