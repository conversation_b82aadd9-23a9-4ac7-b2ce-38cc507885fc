import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useSearchParams, useNavigate } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import { RootState } from "../../api/store";
import { useLazyScrapeProductQuery } from "../../api/services/Products/ScrapingApi";
import {
  setProducts,
  setLoading,
  setError,
  addProducts,
  clearProducts,
  setDynamicMaxPrice,
} from "../../api/services/Products/productSlice";
import ProductGrid from "../../components/product/ProductGrid";
import { Product } from "../../types/Product";
import ModernFilterSection from "../../components/filters/ModernFilterSection";
import { useGetSearchHistoryQuery } from "../../api/services/History/HistoryService";
import { skipToken } from "@reduxjs/toolkit/query";
import { ChatInterface, ChatToggle } from "../../components/chat";
import { openChat, setLastSearchTerm } from "../../api/services/Chat/ChatSlice";

const ProductListingPage: React.FC = () => {
  const { products } = useSelector((state: RootState) => state.product);
  const dispatch = useDispatch();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoadMoreLoading, setIsLoadMoreLoading] = useState(false);
  const [dynamicMaxPrice, setDynamicMaxPriceState] = useState<number>(10000);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [filterResetSignal, setFilterResetSignal] = useState(0);
  const [searching, setSearching] = useState(false);
  const [hasSearchedOnce, setHasSearchedOnce] = useState(false);
  const { isAuthenticated, user } = useSelector(
    (state: RootState) => state.auth
  );
  const { isChatOpen, chatWidth } = useSelector((state: RootState) => state.chat);

  const searchTerm = searchParams.get("search") || "";
  const [triggerScrapeProduct, { data, isLoading, error }] =
    useLazyScrapeProductQuery();
  const {
    refetch, // get this from useGetSearchHistoryQuery
  } = useGetSearchHistoryQuery(isAuthenticated ? undefined : skipToken);

  const productsRef = useRef<Product[]>([]);
  productsRef.current = products;

  //   useEffect(() => {
  //     const fetchProducts = async () => {
  //       if (searchTerm) {
  //         dispatch(clearProducts());
  //         setCurrentPage(1);
  //         setHasMore(true);
  //         dispatch(setLoading(true));
  //         dispatch(setError(null));
  //         setSearching(true);

  //         try {
  //           await triggerScrapeProduct({
  //             query: searchTerm,
  //             page: 1,
  //             userId: isAuthenticated ? user?.id : null,
  //           });
  //         } catch (error) {
  //           console.error("Failed to fetch products:", error);
  //         } finally {
  //           setSearching(false);
  //           setHasSearchedOnce(true);
  //         }
  //       }
  //     };

  //     fetchProducts();

  //   }, [searchTerm, dispatch, triggerScrapeProduct, isAuthenticated, user?.id]);

  useEffect(() => {
    const fetchProducts = async () => {
      if (searchTerm) {
        dispatch(clearProducts());
        setCurrentPage(1);
        setHasMore(true);
        dispatch(setLoading(true));
        dispatch(setError(null));
        setSearching(true);

        try {
          await triggerScrapeProduct({
            query: searchTerm,
            page: 1,
            userId: isAuthenticated ? user?.id : null,
          });

          if (isAuthenticated) {
            await refetch(); // ⬅️ Fetch latest search history from server
          }
        } catch (error) {
          console.error("Failed to fetch products:", error);
        } finally {
          setSearching(false);
          setHasSearchedOnce(true);
        }
      }
    };

    fetchProducts();
  }, [searchTerm, dispatch, triggerScrapeProduct, isAuthenticated, user?.id]);
  useEffect(() => {
    if (data) {
      const newProducts: Product[] =
        data.data?.products?.map((apiProduct: Product) => ({
          ...apiProduct,
        })) || [];

      const existingIds = new Set(productsRef.current.map((p) => p.id));
      const uniqueNewProducts = newProducts.filter(
        (p) => !existingIds.has(p.id)
      );

      if (currentPage === 1) {
        dispatch(setProducts(uniqueNewProducts));
      } else {
        dispatch(addProducts(uniqueNewProducts));
      }

      const prices = uniqueNewProducts
        .map((p) => {
          const priceStr = p.price?.split("-")[0]?.replace(/[^0-9.]/g, "");
          return priceStr ? Number(priceStr) : undefined;
        })
        .filter((price): price is number => typeof price === "number");

      if (prices.length > 0) {
        const maxFoundPrice = Math.ceil(Math.max(...prices));
        setDynamicMaxPriceState(maxFoundPrice);
        dispatch(setDynamicMaxPrice(maxFoundPrice));
      }

      setHasMore(data.data?.hasMore);
      dispatch(setLoading(false));
    }
  }, [data, currentPage, dispatch]);

  useEffect(() => {
    if (error) {
      dispatch(setError("Failed to load products. Please try again."));
      dispatch(setLoading(false));
    }
  }, [error, dispatch]);

  useEffect(() => {
    dispatch(setLoading(isLoading));
  }, [isLoading, dispatch]);

  const loadMore = async () => {
    if (isLoading || isLoadMoreLoading || !hasMore) return;
    setIsLoadMoreLoading(true);
    const nextPage = currentPage + 1;
    setCurrentPage(nextPage);

    await triggerScrapeProduct({
      query: searchTerm,
      page: nextPage,
      userId: isAuthenticated ? user?.id : null,
    });

    setIsLoadMoreLoading(false);
    setFilterResetSignal((prev) => prev + 1);
  };

  useEffect(() => {
    setFilteredProducts(products);
  }, [products]);

  const handleBackToHome = () => {
    navigate("/chat");
  };

  // Handle products update from chat
  const handleChatProductsUpdate = (newProducts: Product[], searchTerm: string) => {
    dispatch(clearProducts());
    dispatch(setProducts(newProducts));
    dispatch(setLastSearchTerm(searchTerm));
    setCurrentPage(1);
    setHasMore(newProducts.length >= 8); // Assuming 8 is the default limit
    setFilterResetSignal(prev => prev + 1);

    // Update URL to reflect the search
    const newSearchParams = new URLSearchParams();
    newSearchParams.set("search", searchTerm);
    navigate(`/products?${newSearchParams.toString()}`, { replace: true });
  };

  // Open chat if search term exists but no chat is open (for Accio-like behavior)
  useEffect(() => {
    if (searchTerm && !isChatOpen) {
      dispatch(openChat());
    }
  }, [searchTerm, isChatOpen, dispatch]);

  return (
    <div className="flex-1 p-6 bg-gray-50 min-h-screen overflow-y-scroll">
      <div className="flex items-center justify-between mb-4">
        <button
          onClick={handleBackToHome}
          className="flex items-center px-3 py-2 text-gray-600 hover:text-pink-600 hover:bg-pink-50 rounded-lg transition-colors duration-200"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Home
        </button>
      </div>

      <ModernFilterSection
        allProducts={products}
        onFiltered={setFilteredProducts}
        maxPriceLimit={dynamicMaxPrice}
        resetSignal={filterResetSignal}
      />

      <ProductGrid products={filteredProducts} searchTerm={searchTerm} />

      {products.length > 0 && (
        <div className="flex flex-col items-center mt-6">
          {hasMore ? (
            <button
              onClick={loadMore}
              className={`px-6 py-2 bg-pink-400 text-white rounded-lg shadow hover:bg-pink-500 transition flex items-center justify-center gap-2 ${
                isLoadMoreLoading ? "opacity-80 cursor-not-allowed" : ""
              }`}
              disabled={isLoadMoreLoading}
            >
              {isLoadMoreLoading ? (
                <>
                  <span className="animate-spin rounded-full h-4 w-4 border-2 border-t-transparent border-white"></span>
                  Please wait, loading...
                </>
              ) : (
                "Load More"
              )}
            </button>
          ) : (
            <p className="text-gray-500">No more data</p>
          )}
        </div>
      )}

      {products.length > 0 &&
        filteredProducts.length === 0 &&
        !isLoading &&
        !error && (
          <p className="text-center text-gray-500 mt-6">
            No products match the selected filters.
          </p>
        )}

      {products.length === 0 &&
        !isLoading &&
        !searching &&
        !error &&
        hasSearchedOnce &&
        searchTerm && (
          <p className="text-center text-gray-500 mt-6">
            No products found for "{searchTerm}".
          </p>
        )}
    </div>
  );
};

export default ProductListingPage;
