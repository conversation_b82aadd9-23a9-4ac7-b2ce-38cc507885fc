import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../api/store";
import { toggleChat, startNewConversation } from "../../api/services/Chat/ChatSlice";
import { MessageCircle, X } from "lucide-react";

interface ChatToggleProps {
  className?: string;
}

const ChatToggle: React.FC<ChatToggleProps> = ({ className = "" }) => {
  const dispatch = useDispatch();
  const { isChatOpen, activeConversationId, currentMessages } = useSelector((state: RootState) => state.chat);

  const handleToggle = () => {
    if (!isChatOpen && !activeConversationId) {
      // If opening chat and no active conversation, start new one
      dispatch(startNewConversation());
    } else {
      dispatch(toggleChat());
    }
  };

  const hasActiveConversation = activeConversationId && currentMessages.length > 0;

  return (
    <div className="fixed bottom-6 right-6 z-40">
      <button
        onClick={handleToggle}
        className={`relative p-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-full shadow-lg hover:from-purple-700 hover:to-pink-700 transition-all duration-300 hover:scale-110 ${className}`}
        title={isChatOpen ? "Close AI Assistant" : "Open AI Assistant"}
      >
        {isChatOpen ? (
          <X className="h-6 w-6" />
        ) : (
          <MessageCircle className="h-6 w-6" />
        )}

        {/* Badge for active conversation */}
        {hasActiveConversation && !isChatOpen && (
          <div className="absolute -top-1 -right-1 w-4 h-4 bg-yellow-400 rounded-full flex items-center justify-center">
            <div className="w-2 h-2 bg-yellow-600 rounded-full animate-pulse"></div>
          </div>
        )}
      </button>
    </div>
  );
};

export default ChatToggle;
